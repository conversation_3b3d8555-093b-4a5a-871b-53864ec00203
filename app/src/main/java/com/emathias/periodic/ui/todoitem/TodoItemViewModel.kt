package com.emathias.periodic.ui.todoitem

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.emathias.periodic.db.dao.ScheduledItemDao
import com.emathias.periodic.service.AiEnhancedTodoService
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.ConfirmScheduledItem
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.GenerateItem
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.HideAddDialog
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.HideConfirmDialog
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.ShowAddDialog
import com.emathias.periodic.ui.scheduleditem.ScheduledItemEvent.ShowConfirmDialog
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class ScheduledItemViewModel(
    private val scheduledItemDao: ScheduledItemDao,
    private val aiService: AiEnhancedTodoService,
) : ViewModel() {

    private val _state = MutableStateFlow(TodoItemState())
    private val _scheduledItems =
        scheduledItemDao.getAll()
            .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())

    val state = combine(_state, _scheduledItems) { state, scheduledItems ->
        state.copy(scheduledItems = scheduledItems)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), TodoItemState())

    fun onEvent(event: TodoItemEvent) {
        when (event) {
//            is Check -> {
//                viewModelScope.launch {
//                    todoItemDao.update(event.todoItem.copy(checked = true))
//                }
//            }
//
//            is Uncheck -> {
//                viewModelScope.launch {
//                    todoItemDao.update(event.todoItem.copy(checked = false))
//                }
//            }

            ShowAddDialog -> _state.update { it.copy(showingAddDialog = true) }

            HideAddDialog -> _state.update { it.copy(showingAddDialog = false) }

            is ShowConfirmDialog -> _state.update {
                it.copy(
                    showingConfirmDialog = true,
                    pendingScheduledItem = event.scheduledItem,
                )
            }

            HideConfirmDialog -> _state.update {
                it.copy(
                    showingConfirmDialog = false,
                    pendingScheduledItem = null,
                )
            }

//            is AddItem -> viewModelScope.launch {
//                todoItemDao.insert(event.todoItem)
//            }

            is GenerateItem -> viewModelScope.launch {
                val scheduledItem = aiService.generateScheduledItem(event.prompt).getOrThrow()
                onEvent(HideAddDialog)
                onEvent(ShowConfirmDialog(scheduledItem))
            }

            is ConfirmScheduledItem -> viewModelScope.launch {
                scheduledItemDao.insert(event.scheduledItem)
                onEvent(HideConfirmDialog)
            }
        }
    }
}
